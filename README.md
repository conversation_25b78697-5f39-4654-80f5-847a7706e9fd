# Omnichannel Inbox System

## Overview
The Omnichannel Inbox System is a centralized messaging platform designed for e-commerce store owners to manage customer conversations from multiple channels (Facebook, Zalo, etc.) in a single interface. This system helps businesses provide efficient and consistent customer service across all integrated platforms.

## Features
- **Unified Inbox**: Consolidate messages from multiple channels into a single view
- **Real-time Messaging**: Send and receive messages with customers in real-time
- **User Management**: Role-based access control for Store Owners and Customer Service Representatives
- **Conversation Assignment**: Distribute conversations among team members
- **Status Tracking**: Monitor the progress of customer interactions
- **Message Templates**: Create and use templates for common responses
- **Search Functionality**: Quickly find conversations and messages
- **Multi-channel Integration**: Currently supports Facebook Messenger and Zalo, with extensibility for more channels

## Technology Stack
- **Backend**: Node.js with NestJS framework
- **Frontend**: React.js with TypeScript and Vite
- **Database**: MongoDB
- **Real-time Communication**: Socket.IO
- **Authentication**: JWT (JSON Web Tokens)

## Project Structure
```
omnichannel-inbox/
├── backend/              # NestJS application
│   ├── src/              # Source code
│   │   ├── auth/         # Authentication module
│   │   ├── users/        # User management module
│   │   ├── conversations/ # Conversation management module
│   │   ├── messages/     # Messaging module
│   │   ├── templates/    # Template management module
│   │   ├── search/       # Search module
│   │   ├── channels/     # External channel adapters
│   │   └── ...           # Other modules
│   └── ...               # Configuration files
├── frontend/             # React application
│   ├── src/              # Source code
│   │   ├── components/   # Reusable UI components
│   │   ├── pages/        # Page components
│   │   ├── services/     # API service layer
│   │   ├── store/        # State management
│   │   └── ...           # Other directories
│   └── ...               # Configuration files
├── docs/                 # Project documentation
│   ├── prd.md            # Product Requirements Document
│   ├── architecture.md   # System Architecture
│   ├── frontend-spec.md  # Frontend Specifications
│   ├── stories/          # User stories
│   └── roadmap.md        # Project roadmap
└── ...
```

## Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm (v7 or higher) or yarn (v1.22 or higher)
- MongoDB (v4.4 or higher) or MongoDB Atlas account
- Docker and Docker Compose (optional, for containerized deployment)

### Installation
1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd albatross-omnichannel-inbox
   ```

2. Install root dependencies:
   ```bash
   npm install
   ```

3. Install backend dependencies:
   ```bash
   cd backend
   npm install
   cd ..
   ```

4. Install frontend dependencies:
   ```bash
   cd frontend
   npm install
   cd ..
   ```

### Environment Setup
Create `.env` files in both `backend` and `frontend` directories based on the provided examples:
- `backend/.env.example`
- `frontend/.env.example`

### Running the Application

#### Development Mode
To start both the backend and frontend in development mode:
```bash
npm run dev
```

This will start:
- Backend API on http://localhost:3000
- Frontend application on http://localhost:5173

#### Running Backend Only
```bash
cd backend
npm run start:dev
```

#### Running Frontend Only
```bash
cd frontend
npm run dev
```

### Building for Production
To build both applications for production:
```bash
npm run build
```

## Docker Support

This project includes Docker configurations for both development and production environments.

### Development with Docker
```bash
# Start development services
make docker-dev-up

# View logs
make docker-dev-logs

# Stop services
make docker-dev-down
```

### Production with Docker
```bash
# Build and start production services
make docker-up

# View logs
make docker-logs

# Stop services
make docker-down
```

## Makefile Commands

The project includes a Makefile with useful commands for development:

```bash
# Show all available commands
make help

# Start development environment
make dev

# Run tests
make test

# Build applications
make build

# Docker commands
make docker-up      # Start production containers
make docker-dev-up  # Start development containers
```

For a complete list of commands, run `make help`.

## Documentation
- [Product Requirements Document](docs/prd.md)
- [System Architecture](docs/architecture.md)
- [Frontend Specifications](docs/frontend-spec.md)
- [Project Roadmap](docs/roadmap.md)
- [User Stories](docs/stories/)
- [Zalo Personal Adapter - Technical Design](docs/architecture/zalo-personal-adapter-design.md)
- [Zalo Personal Frontend Design](docs/frontend-spec/zalo-personal-frontend-design.md)
- [Technical Documentation](docs/technical-documentation.md)
- [Development Setup Guide](docs/development-setup.md)

## Development Plan
Please refer to our [Project Roadmap](docs/roadmap.md) for detailed timeline and milestones.

## Contributing
Contributing guidelines will be added in the future.

## License
License information will be added in the future.