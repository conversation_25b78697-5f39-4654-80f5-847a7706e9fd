version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:5.0
    container_name: omnichannel-mongodb-dev
    restart: always
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
    volumes:
      - mongodb_data_dev:/data/db
    networks:
      - omnichannel-network-dev

  # Backend NestJS Application
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: omnichannel-backend-dev
    restart: always
    ports:
      - "3000:3000"
    environment:
      - MONGODB_URI=*************************************************************************
      - JWT_SECRET=your-super-secret-jwt-key-change-in-production
      - JWT_EXPIRES_IN=3600
      - FACEBOOK_APP_ID=your-facebook-app-id
      - FACEBOOK_APP_SECRET=your-facebook-app-secret
      - ZALO_APP_ID=your-zalo-app-id
      - ZALO_APP_SECRET=your-zalo-app-secret
    volumes:
      - ./backend:/app
      - /app/node_modules
    depends_on:
      - mongodb
    networks:
      - omnichannel-network-dev

  # Frontend React Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: omnichannel-frontend-dev
    restart: always
    ports:
      - "5173:5173"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - omnichannel-network-dev

# Define named volumes
volumes:
  mongodb_data_dev:

# Define networks
networks:
  omnichannel-network-dev:
    driver: bridge