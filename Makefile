# Makefile for Omnichannel Inbox System

# Variables
DOCKER_COMPOSE = docker-compose
DOCKER_COMPOSE_DEV = docker-compose -f docker-compose.dev.yml
NPM = npm
DOCKER = docker

# Default target
.PHONY: help
help: ## Show this help
	@echo "Omnichannel Inbox System - Makefile Commands"
	@echo ""
	@echo "Usage: make [command]"
	@echo ""
	@echo "Commands:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "\033[36m%-20s\033[0m %s\n", $1, $2}' $(MAKEFILE_LIST)

# Development commands
.PHONY: dev
dev: ## Start development environment
	$(NPM) run dev

.PHONY: dev-backend
dev-backend: ## Start backend in development mode
	cd backend && $(NPM) run start:dev

.PHONY: dev-frontend
dev-frontend: ## Start frontend in development mode
	cd frontend && $(NPM) run dev

# Build commands
.PHONY: build
build: ## Build both backend and frontend
	$(NPM) run build

.PHONY: build-backend
build-backend: ## Build backend
	cd backend && $(NPM) run build

.PHONY: build-frontend
build-frontend: ## Build frontend
	cd frontend && $(NPM) run build

# Test commands
.PHONY: test
test: ## Run tests for both backend and frontend
	$(NPM) run test

.PHONY: test-backend
test-backend: ## Run backend tests
	cd backend && $(NPM) run test

.PHONY: test-frontend
test-frontend: ## Run frontend tests
	cd frontend && $(NPM) run test

# Docker commands
.PHONY: docker-up
docker-up: ## Start all services with Docker Compose
	$(DOCKER_COMPOSE) up -d

.PHONY: docker-down
docker-down: ## Stop all services with Docker Compose
	$(DOCKER_COMPOSE) down

.PHONY: docker-logs
docker-logs: ## View logs from Docker containers
	$(DOCKER_COMPOSE) logs -f

.PHONY: docker-build
docker-build: ## Build Docker images
	$(DOCKER_COMPOSE) build

.PHONY: docker-rebuild
docker-rebuild: ## Rebuild Docker images
	$(DOCKER_COMPOSE) build --no-cache

.PHONY: docker-start
docker-start: ## Start Docker containers
	$(DOCKER_COMPOSE) start

.PHONY: docker-stop
docker-stop: ## Stop Docker containers
	$(DOCKER_COMPOSE) stop

.PHONY: docker-restart
docker-restart: ## Restart Docker containers
	$(DOCKER_COMPOSE) restart

.PHONY: docker-status
docker-status: ## Show status of Docker containers
	$(DOCKER_COMPOSE) ps

# Docker development commands
.PHONY: docker-dev-up
docker-dev-up: ## Start development services with Docker Compose
	$(DOCKER_COMPOSE_DEV) up -d

.PHONY: docker-dev-down
docker-dev-down: ## Stop development services with Docker Compose
	$(DOCKER_COMPOSE_DEV) down

.PHONY: docker-dev-logs
docker-dev-logs: ## View logs from development Docker containers
	$(DOCKER_COMPOSE_DEV) logs -f

.PHONY: docker-dev-build
docker-dev-build: ## Build development Docker images
	$(DOCKER_COMPOSE_DEV) build

.PHONY: docker-dev-rebuild
docker-dev-rebuild: ## Rebuild development Docker images
	$(DOCKER_COMPOSE_DEV) build --no-cache

.PHONY: docker-dev-start
docker-dev-start: ## Start development Docker containers
	$(DOCKER_COMPOSE_DEV) start

.PHONY: docker-dev-stop
docker-dev-stop: ## Stop development Docker containers
	$(DOCKER_COMPOSE_DEV) stop

.PHONY: docker-dev-restart
docker-dev-restart: ## Restart development Docker containers
	$(DOCKER_COMPOSE_DEV) restart

.PHONY: docker-dev-status
docker-dev-status: ## Show status of development Docker containers
	$(DOCKER_COMPOSE_DEV) ps

# Installation commands
.PHONY: install
install: ## Install dependencies for both backend and frontend
	$(NPM) install
	cd backend && $(NPM) install
	cd frontend && $(NPM) install

.PHONY: install-backend
install-backend: ## Install backend dependencies
	cd backend && $(NPM) install

.PHONY: install-frontend
install-frontend: ## Install frontend dependencies
	cd frontend && $(NPM) install

# Clean commands
.PHONY: clean
clean: ## Clean build artifacts
	cd backend && rm -rf dist node_modules
	cd frontend && rm -rf dist node_modules
	rm -rf node_modules

.PHONY: clean-backend
clean-backend: ## Clean backend build artifacts
	cd backend && rm -rf dist node_modules

.PHONY: clean-frontend
clean-frontend: ## Clean frontend build artifacts
	cd frontend && rm -rf dist node_modules

# Linting commands
.PHONY: lint
lint: ## Run linting for both backend and frontend
	$(NPM) run lint

.PHONY: lint-backend
lint-backend: ## Run backend linting
	cd backend && $(NPM) run lint

.PHONY: lint-frontend
lint-frontend: ## Run frontend linting
	cd frontend && $(NPM) run lint

# Database commands
.PHONY: db-up
db-up: ## Start MongoDB container only
	$(DOCKER_COMPOSE) up -d mongodb

.PHONY: db-down
db-down: ## Stop MongoDB container
	$(DOCKER_COMPOSE) stop mongodb

.PHONY: db-logs
db-logs: ## View MongoDB logs
	$(DOCKER_COMPOSE) logs -f mongodb

.PHONY: db-dev-up
db-dev-up: ## Start MongoDB development container only
	$(DOCKER_COMPOSE_DEV) up -d mongodb

.PHONY: db-dev-down
db-dev-down: ## Stop MongoDB development container
	$(DOCKER_COMPOSE_DEV) stop mongodb

.PHONY: db-dev-logs
db-dev-logs: ## View MongoDB development logs
	$(DOCKER_COMPOSE_DEV) logs -f mongodb