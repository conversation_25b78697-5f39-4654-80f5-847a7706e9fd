import {
  Injectable,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as bcrypt from 'bcrypt';
import { User, UserDocument, UserRole, UserStatus } from './user.entity';
import { CreateUserDto } from './dto/create-user.dto';

@Injectable()
export class UserService {
  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
  ) {}

  async create(createUserDto: CreateUserDto, createdBy?: string): Promise<User> {
    // Check if user with this email already exists
    const existingUserByEmail = await this.userModel
      .findOne({ email: createUserDto.email })
      .exec();
    if (existingUserByEmail) {
      throw new BadRequestException('Email already exists');
    }

    // Check if user with this username already exists (if username is provided)
    if (createUserDto.username) {
      const existingUser = await this.userModel.findOne({
        username: createUserDto.username,
      });
      if (existingUser) {
        throw new BadRequestException('Username already exists');
      }
    }

    // Hash the password
    const saltRounds = 12; // Increased from 10 for better security
    const hashedPassword = await bcrypt.hash(createUserDto.password, saltRounds);

    // Create user
    const createdUser = new this.userModel({
      ...createUserDto,
      password: hashedPassword,
      role: UserRole.CSKH, // Default role
      status: UserStatus.ACTIVE, // User is active immediately
      createdBy: createdBy ? createdBy : null,
    });

    const savedUser = await createdUser.save();
    
    return savedUser;
  }

  async findByEmail(email: string): Promise<User | null> {
    return this.userModel.findOne({ email }).exec();
  }

  async findByUsername(username: string): Promise<User | null> {
    return this.userModel.findOne({ username }).exec();
  }

  async findById(id: string): Promise<User | null> {
    return this.userModel.findById(id).exec();
  }

  async validateUser(email: string, password: string): Promise<User | null> {
    const user = await this.findByEmail(email);
    if (user && (await bcrypt.compare(password, user.password))) {
      return user;
    }
    return null;
  }

  private isPasswordStrong(password: string): boolean {
    const strongPasswordRegex =
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    return strongPasswordRegex.test(password);
  }
}
