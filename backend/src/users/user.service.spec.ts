import { Test, TestingModule } from '@nestjs/testing';
import { UserService } from './user.service';
import { getModelToken } from '@nestjs/mongoose';
import { User } from './user.entity';
import { ConflictException } from '@nestjs/common';

describe('UserService', () => {
  let userService: UserService;
  let userModel: any;

  const mockUserModel = {
    findOne: jest.fn(),
    new: jest.fn().mockReturnThis(),
    constructor: jest.fn().mockReturnThis(),
    save: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserService,
        {
          provide: getModelToken(User.name),
          useValue: mockUserModel,
        },
      ],
    }).compile();

    userService = module.get<UserService>(UserService);
    userModel = module.get(getModelToken(User.name));
  });

  it('should be defined', () => {
    expect(userService).toBeDefined();
  });

  describe('create', () => {
    it('should create a new user', async () => {
      const createUserDto = {
        name: 'John Doe',
        username: 'johndoe',
        password: 'Password123!',
      };

      const mockUser = {
        ...createUserDto,
        id: '1',
        role: 'cskh',
        status: 'active',
        createdBy: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        save: jest.fn().mockResolvedValue({
          ...createUserDto,
          id: '1',
          role: 'cskh',
          status: 'active',
          createdAt: new Date(),
          updatedAt: new Date(),
        }),
      };

      userModel.findOne.mockResolvedValue(null);
      userModel.new.mockReturnValue(mockUser);
      mockUser.save.mockResolvedValue(mockUser);

      const result = await userService.create(createUserDto);

      expect(result).toBeDefined();
      expect(userModel.findOne).toHaveBeenCalledWith({
        username: createUserDto.username,
      });
      // Verify the new behavior
    });

    it('should throw ConflictException if user already exists', async () => {
      const createUserDto = {
        name: 'John Doe',
        username: 'johndoe',
        password: 'Password123!',
      };

      userModel.findOne.mockResolvedValue({ username: createUserDto.username });

      await expect(userService.create(createUserDto)).rejects.toThrow(
        ConflictException,
      );
    });
  });
});
