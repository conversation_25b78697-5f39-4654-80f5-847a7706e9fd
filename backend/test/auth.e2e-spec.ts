// Test user registration and login
const request = require('supertest');
const app = require('../src/main'); // Adjust path as needed

describe('Auth API', () => {
  const testUser = {
    username: 'testuser',
    password: 'testpassword123',
  };

  it('should register a new user', async () => {
    const res = await request(app)
      .post('/api/auth/register')
      .send(testUser)
      .expect(201);

    expect(res.body).toHaveProperty('id');
    expect(res.body.username).toBe(testUser.username);
    expect(res.body).toHaveProperty('role');
    expect(res.body).toHaveProperty('status');
  });

  it('should login with registered user', async () => {
    const res = await request(app)
      .post('/api/auth/login')
      .send(testUser)
      .expect(200);

    expect(res.body).toHaveProperty('access_token');
    expect(res.body.user.username).toBe(testUser.username);
  });
});