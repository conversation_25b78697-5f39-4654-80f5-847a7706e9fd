{"name": "omnichannel-inbox", "version": "1.0.0", "description": "Omnichannel Inbox System", "scripts": {"dev": "concurrently \"cd backend && npm run start:dev\" \"cd frontend && npm run dev\"", "build": "cd backend && npm run build && cd ../frontend && npm run build", "test": "cd backend && npm run test && cd ../frontend && npm run test", "lint": "cd backend && npm run lint && cd ../frontend && npm run lint"}, "keywords": ["omnichannel", "inbox", "messaging", "customer-service"], "author": "Albatross Foundation", "license": "MIT", "devDependencies": {"concurrently": "^7.6.0"}}