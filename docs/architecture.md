# Omnichannel Inbox System Architecture

## 1. Overview

### 1.1 Purpose
This document describes the system architecture for the Omnichannel Inbox System. It provides a comprehensive technical blueprint for building a centralized messaging platform that consolidates customer conversations from multiple channels (including Facebook and both types of Zalo accounts) into a single interface.

### 1.2 Scope
The architecture covers:
- High-level system components and their interactions
- Technology stack selection
- Database design
- API specifications
- Security considerations
- Deployment architecture
- Support for multiple Zalo integration methods:
  - Zalo Official Account (OA) via official API
  - Zalo Personal Account via unofficial zca-js library

### 1.3 Goals
- Build a scalable, secure, and performant messaging platform
- Support real-time communication with customers across multiple channels
- Provide an intuitive interface for store owners and customer service representatives
- Accommodate approximately 1000 concurrent users
- Enable future extensibility for additional channels
- Support both official and unofficial Zalo integration methods

## 2. System Context

### 2.1 System Environment
The Omnichannel Inbox System is a web-based application that integrates with external messaging platforms:
- Facebook Messenger API
- Zalo Official Account API
- Zalo Personal Account via zca-js library

### 2.2 External Systems
- **Facebook Messenger**: Provides messaging capabilities through its official API
- **Zalo OA**: Provides messaging capabilities through <PERSON>alo's official API for business accounts
- **Zalo Personal**: Provides messaging capabilities through the unofficial zca-js library for personal accounts
- **Web Browsers**: Primary user interface accessed through modern browsers

### 2.3 Users
- **Store Owners**: Monitor and manage customer service performance
- **Customer Service Representatives (CSKH)**: Respond to customer inquiries

## 3. Technology Stack

### 3.1 Backend
- **Runtime Environment**: Node.js
- **Framework**: NestJS
- **Database**: MongoDB
- **Real-time Communication**: Socket.IO
- **Authentication**: JWT (JSON Web Tokens)
- **API Documentation**: Swagger/OpenAPI

### 3.2 Frontend
- **Framework**: React.js (with potential for Vue.js as alternative)
- **State Management**: Redux or Vuex (depending on chosen framework)
- **UI Components**: Material-UI or Vuetify
- **Real-time Updates**: Socket.IO client
- **Build Tool**: Webpack
- **Package Manager**: npm or yarn

### 3.3 Infrastructure
- **Cloud Provider**: AWS
- **Containerization**: Docker
- **Orchestration**: Kubernetes (for production) or AWS ECS
- **Load Balancing**: AWS Application Load Balancer
- **Database Hosting**: MongoDB Atlas or self-managed MongoDB cluster
- **CI/CD**: GitHub Actions/Jenkins
- **Monitoring**: AWS CloudWatch
- **File Storage**: AWS S3
- **Caching**: AWS ElastiCache (Redis)

## 4. Architectural Design

### 4.1 High-Level Architecture
```
┌─────────────────────────────────────┐
│           Load Balancer             │
│              (AWS ELB)              │
└────────────────┬────────────────────┘
                 │
┌────────────────────────────────────────────────────────────────────┐
│                    Frontend Application                            │
│                         (React.js)                                 │
└────────────────────────────┬───────────────────────────────────────┘
                             │
┌────────────────────────────▼───────────────────────────────────────┐
│                        API Gateway                                 │
│                        (NestJS - AWS ECS)                          │
└────────────────────┬───────────────────────────────────────────────┘
                     │
┌────────────────────▼───────────────────────────────────────────────┐
│                     Socket.IO Server                               │
│                          (Real-time)                               │
└────────────────────────────────────────────────────────────────────┘
                     │
    ┌────────────────┼───────────────────────────────────────────────┐
    │                │                                               │
┌───▼────┐    ┌──────▼──────┐    ┌─────────────┐    ┌─────────────────┐
│MongoDB │    │ Redis Cache │    │ Channel     │    │ Channel         │
│(AWS    │    │(AWS         │    │ Adapter 1   │    │ Adapter N       │
│Document│    │ElastiCache) │    │(Facebook)   │    │(Future)         │
│  DB)   │    │             │    │             │    │                 │
└────────┘    └─────────────┘    └─────────────┘    └─────────────────┘
```

### 4.2 Component Diagram
- **Web Client**: React/Vue.js frontend application running in user browsers
- **Load Balancer**: AWS Application Load Balancer for distributing traffic
- **API Gateway**: NestJS application handling REST API requests
- **Socket.IO Server**: Real-time communication server
- **Authentication Module**: Manages user authentication and authorization
- **User Management Module**: Handles user CRUD operations
- **Conversation Module**: Implements conversation management functionality
- **Messaging Module**: Handles real-time messaging and message persistence
- **Template Module**: Manages message templates
- **Search Module**: Implements search functionality across conversations
- **Analytics Module**: Provides performance analytics and reporting
- **Channel Adapter Module**: Interface with external messaging platforms
- **Data Access Layer**: Manages database operations
- **MongoDB**: Primary database for storing users, conversations, and messages
- **Redis**: Caching layer for session data and real-time messaging
- **AWS S3**: File storage for attachments

### 4.3 Deployment Architecture
```
                    ┌─────────────────────────────┐
                    │    AWS Application          │
                    │      Load Balancer          │
                    └─────────────┬───────────────┘
                                  │
        ┌─────────────────────────┼──────────────────────────┐
        │                         │                          │
┌───────▼────────┐      ┌────────▼────────┐      ┌──────────▼────────┐
│   Frontend     │      │   Backend API   │      │  Socket.IO Server │
│   (S3 +        │      │   (AWS ECS)     │      │   (AWS ECS)       │
│ CloudFront)    │      │                 │      │                   │
└────────────────┘      └─────────────────┘      └───────────────────┘
                                  │                          │
                        ┌────────▼────────┐      ┌──────────▼────────┐
                        │    MongoDB      │      │   External APIs   │
                        │   (DocumentDB)  │      │ (Facebook, Zalo)  │
                        └─────────────────┘      └───────────────────┘
                                  │
                        ┌────────▼────────┐
                        │   Redis Cache   │
                        │ (ElastiCache)   │
                        └─────────────────┘
```

## 5. Database Design

### 5.1 Database Schema
- **Users Collection**:
  - `_id`: ObjectId
  - `username`: String
  - `email`: String
  - `password`: String (hashed)
  - `role`: String (Store Owner, CSKH)
  - `status`: String (Active, Inactive)
  - `createdAt`: Date
  - `updatedAt`: Date

- **Conversations Collection**:
  - `_id`: ObjectId
  - `channel`: String (Facebook, Zalo-OA, Zalo-Personal)
  - `customerId`: String (External ID from channel)
  - `customerName`: String
  - `assigneeId`: ObjectId (Reference to Users)
  - `status`: String (Open, Pending, Closed)
  - `lastMessageAt`: Date
  - `createdAt`: Date
  - `updatedAt`: Date
  - `closedAt`: Date (Optional)

- **Messages Collection**:
  - `_id`: ObjectId
  - `conversationId`: ObjectId (Reference to Conversations)
  - `senderId`: ObjectId (Reference to Users, null if customer)
  - `content`: String
  - `attachments`: Array of URLs
  - `timestamp`: Date
  - `isRead`: Boolean
  - `direction`: String (Inbound, Outbound)
  - `status`: String (Sent, Delivered, Read)

- **Templates Collection**:
  - `_id`: ObjectId
  - `title`: String
  - `content`: String
  - `createdBy`: ObjectId (Reference to Users)
  - `createdAt`: Date
  - `updatedAt`: Date

- **ChannelConfigs Collection**:
  - `_id`: ObjectId
  - `channel`: String (Facebook, Zalo-OA, Zalo-Personal, etc.)
  - `config`: Object (Channel-specific configuration)
  - `isActive`: Boolean
  - `createdAt`: Date
  - `updatedAt`: Date

### 5.2 Indexes
- Compound index on Conversations collection: `{ channel: 1, customerId: 1 }`
- Index on Messages collection: `{ conversationId: 1, timestamp: -1 }`
- Index on Users collection: `{ email: 1 }`
- Text indexes for search functionality on Conversations and Messages collections

## 6. API Design

### 6.1 Authentication Controller
- `POST /api/auth/register`: Register a new user
- `POST /api/auth/login`: Authenticate user and return JWT
- `POST /api/auth/logout`: Invalidate user session

### 6.2 User Management Controller
- `GET /api/users`: Get list of users (Store Owner only)
- `GET /api/users/:id`: Get user details
- `PUT /api/users/:id`: Update user information
- `DELETE /api/users/:id`: Delete user (Store Owner only)

### 6.3 Conversation Controller
- `GET /api/conversations`: Get list of conversations with filtering/pagination
- `GET /api/conversations/:id`: Get conversation details with messages
- `PUT /api/conversations/:id/assign`: Assign conversation to a user
- `PUT /api/conversations/:id/status`: Update conversation status
- `POST /api/conversations/:id/messages`: Send a message in conversation

### 6.4 Template Controller
- `GET /api/templates`: Get list of message templates
- `POST /api/templates`: Create a new message template
- `PUT /api/templates/:id`: Update a message template
- `DELETE /api/templates/:id`: Delete a message template

### 6.5 Search Controller
- `GET /api/search/conversations`: Search conversations by keywords

### 6.6 Analytics Controller
- `GET /api/analytics/performance`: Get performance metrics
- `GET /api/analytics/user/:id`: Get specific user performance data

## 7. Security Considerations

### 7.1 Authentication
- Implementation of JWT for stateless authentication
- Secure password storage using bcrypt
- Session management with expiration
- Refresh token mechanism for long-lived sessions

### 7.2 Authorization
- Role-based access control (RBAC)
- Middleware to verify permissions for each API endpoint
- Field-level permissions for sensitive data

### 7.3 Data Protection
- HTTPS encryption for all data in transit
- MongoDB encryption at rest (if using MongoDB Atlas)
- Input validation and sanitization to prevent injection attacks
- Protection against Cross-Site Request Forgery (CSRF)

### 7.4 API Security
- Rate limiting to prevent abuse
- CORS configuration to restrict origins
- Helmet.js for securing Express apps with various HTTP headers
- Input validation using class-validator

## 8. Performance Considerations

### 8.1 Scalability
- Horizontal scaling of backend services using containerization
- Database sharding for large datasets
- Caching frequently accessed data with Redis
- CDN for static assets

### 8.2 Real-time Communication
- Implementation of WebSocket (Socket.IO) for real-time messaging
- Efficient event handling to minimize latency
- Room-based messaging to optimize message distribution

### 8.3 Database Optimization
- Proper indexing strategy for query performance
- Aggregation pipelines for complex data retrieval
- Connection pooling for database connections

## 9. Deployment Considerations

### 9.1 Environment Setup
- Development, staging, and production environments
- Environment-specific configuration files
- Infrastructure as Code using AWS CloudFormation or Terraform

### 9.2 CI/CD Pipeline
- Automated testing on each commit
- Automated deployment to staging environment
- Manual approval for production deployment
- Blue-green deployment strategy for zero-downtime releases

### 9.3 Monitoring and Logging
- Application performance monitoring (APM) with AWS CloudWatch
- Centralized logging solution
- Health checks for all services
- Alerting for critical system events
- Custom metrics for business KPIs

## 10. Future Extensibility

### 10.1 Additional Channels
- Modular design of messaging adapters to easily add new channels
- Standardized interface for channel integration
- Plugin architecture for third-party channel adapters
- Support for multiple integration methods per channel (e.g., Zalo OA and Zalo Personal)

### 10.2 Zalo Integration Types
The system supports two distinct integration methods for Zalo:

#### 10.2.1 Zalo Official Account (OA) Integration
- Uses Zalo's official API
- Requires business verification
- Officially supported by Zalo
- More stable and reliable
- Better rate limiting and error handling

#### 10.2.2 Zalo Personal Account Integration
- Uses the unofficial zca-js library
- Works with personal Zalo accounts
- May violate Zalo's terms of service
- Less stable due to dependency on unofficial library
- Requires careful handling to avoid account blocking

### 10.3 Adapter Design for Multiple Integration Methods
The Channel Adapter Pattern has been extended to support multiple integration methods for the same platform:

```
channels/
├── channel.adapter.interface.ts     # Common interface for all adapters
├── channel.manager.ts               # Manages all channel adapters
├── facebook/
│   └── facebook.adapter.ts          # Facebook integration
├── zalo/
│   ├── zalo.oa.adapter.ts           # Zalo OA official API integration
│   ├── zalo.personal.adapter.ts     # Zalo Personal integration via zca-js
│   └── zalo.config.interface.ts     # Common configuration interface
└── ...
```

## 11. Non-Functional Requirements Implementation

### 11.1 Performance
- Load testing with tools like Artillery or k6
- Database query optimization
- Caching strategies for frequently accessed data
- CDN for static assets

### 11.2 Usability
- Responsive design for various screen sizes
- Intuitive user interface with clear navigation
- Keyboard shortcuts for power users
- Accessibility compliance (WCAG 2.1 AA)

### 11.3 Reliability
- Health checks for all services
- Automated failover mechanisms
- Regular backups of critical data
- Disaster recovery plan

## 12. Conclusion

This architecture provides a solid foundation for building the Omnichannel Inbox System using NestJS. It addresses the key requirements of consolidating messages from multiple channels, supporting real-time communication, and providing role-based access control. The modular design of NestJS allows for better organization and scalability, while the chosen technology stack ensures performance and scalability for up to 1000 concurrent users. The use of NestJS will provide additional benefits such as:

- Built-in support for TypeScript
- Modular architecture with dependency injection
- Built-in support for WebSockets (Socket.IO)
- Easy integration with Swagger for API documentation
- Better testing capabilities
- Enhanced maintainability and code organization

The architecture is designed to be deployed on AWS, leveraging services like ECS for container orchestration, DocumentDB as a MongoDB-compatible database, ElastiCache for Redis caching, S3 for file storage, and CloudFront for content delivery. This approach ensures high availability, scalability, and reliability while taking advantage of AWS's managed services to reduce operational overhead.