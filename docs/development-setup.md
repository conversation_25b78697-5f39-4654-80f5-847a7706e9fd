# Development Setup Guide

## Prerequisites
Before you begin, ensure you have the following installed on your system:
- Node.js (v16 or higher)
- npm (v7 or higher) or yarn (v1.22 or higher)
- MongoDB (v4.4 or higher) or MongoDB Atlas account
- Git

## Project Setup

### 1. Clone the Repository
```bash
git clone <repository-url>
cd albatross-omnichannel-inbox
```

### 2. Environment Variables
Create `.env` files in both backend and frontend directories:

**Backend (.env)**
```env
PORT=3000
MONGODB_URI=mongodb://localhost:27017/omnichannel-inbox
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=3600
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret
ZALO_APP_ID=your-zalo-app-id
ZALO_APP_SECRET=your-zalo-app-secret
```

**Frontend (.env)**
```env
VITE_API_URL=http://localhost:3000/api
VITE_SOCKET_URL=http://localhost:3000
```

### 3. Backend Setup
```bash
cd backend
npm install
npm run start:dev
```

The backend will start on `http://localhost:3000`.

### 4. Frontend Setup
In a new terminal:
```bash
cd frontend
npm install
npm start
```

The frontend will start on `http://localhost:5173`.

## Development Workflow

### Branching Strategy
- `main` - Production-ready code
- `develop` - Integration branch for features
- `feature/*` - Feature branches
- `bugfix/*` - Bug fix branches
- `release/*` - Release preparation branches

### Git Workflow
1. Create a feature branch from `develop`:
   ```bash
   git checkout develop
   git pull origin develop
   git checkout -b feature/your-feature-name
   ```

2. Make your changes and commit:
   ```bash
   git add .
   git commit -m "feat: description of your changes"
   ```

3. Push your branch and create a pull request:
   ```bash
   git push origin feature/your-feature-name
   ```

### Code Style
- Follow the established linting rules (ESLint for both frontend and backend)
- Use Prettier for code formatting
- Write meaningful commit messages following conventional commits

### Testing
- Run unit tests: `npm test`
- Run unit tests with coverage: `npm run test:cov`
- Run end-to-end tests: `npm run test:e2e`

## Database Setup

### Local MongoDB
If you don't have MongoDB installed locally, you can use Docker:
```bash
docker run --name mongodb -p 27017:27017 -d mongo
```

### MongoDB Atlas
1. Create a MongoDB Atlas account
2. Create a new cluster
3. Add your IP address to the whitelist
4. Create a database user
5. Update your `.env` file with the connection string

## API Documentation
Once the backend is running, you can access the API documentation at:
```
http://localhost:3000/api
```

## Troubleshooting

### Common Issues

1. **Port already in use**
   - Change the PORT value in your `.env` file
   - Or kill the process using the port:
     ```bash
     lsof -i :3000
     kill -9 <PID>
     ```

2. **MongoDB connection error**
   - Ensure MongoDB is running
   - Check your MONGODB_URI in the `.env` file
   - Verify network connectivity if using MongoDB Atlas

3. **JWT token issues**
   - Ensure JWT_SECRET is set in your `.env` file
   - Make sure the secret is sufficiently complex

4. **CORS errors**
   - Check the frontend VITE_API_URL matches the backend URL
   - Verify CORS configuration in the backend

### Getting Help
If you encounter issues not covered in this guide:
1. Check the project documentation in the `docs/` directory
2. Contact the project team members
3. Create an issue in the repository

## Useful Commands

### Backend
- `npm run start` - Start the application
- `npm run start:dev` - Start the application in development mode with hot reload
- `npm run build` - Build the application
- `npm test` - Run tests
- `npm run test:cov` - Run tests with coverage
- `npm run test:e2e` - Run end-to-end tests
- `npm run lint` - Run linting
- `npm run lint:fix` - Run linting and fix issues

### Frontend
- `npm start` - Start the development server
- `npm run build` - Build the application for production
- `npm test` - Run tests
- `npm run eject` - Eject from Create React App (irreversible)
- `npm run lint` - Run linting
- `npm run lint:fix` - Run linting and fix issues