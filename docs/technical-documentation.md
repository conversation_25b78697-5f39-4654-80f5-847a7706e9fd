# Technical Documentation: Omnichannel Inbox System

## Table of Contents
1. [System Architecture](#system-architecture)
2. [Backend Implementation](#backend-implementation)
3. [Frontend Implementation](#frontend-implementation)
4. [Database Design](#database-design)
5. [API Documentation](#api-documentation)
6. [Real-time Communication](#real-time-communication)
7. [Security Considerations](#security-considerations)
8. [Deployment](#deployment)
9. [Testing](#testing)

## System Architecture

The Omnichannel Inbox System follows a microservices-inspired architecture with a monolithic implementation. The system consists of:

1. **Frontend Application**: React.js single-page application
2. **Backend API**: NestJS application with modular structure
3. **Database**: MongoDB for data persistence
4. **Real-time Server**: Socket.IO for real-time messaging
5. **External Integrations**: Adapters for Facebook Messenger and Zalo APIs

For detailed architecture, refer to the [System Architecture Document](architecture.md).

## Backend Implementation

### Technology Stack
- **Runtime**: Node.js (v16+)
- **Framework**: NestJS (v9+)
- **Database**: MongoDB with Mongoose ODM
- **Real-time**: Socket.IO (v4+)
- **Authentication**: JWT
- **Testing**: Jest
- **Documentation**: Swagger/OpenAPI

### Project Structure
```
backend/
├── src/
│   ├── auth/              # Authentication module
│   ├── users/             # User management module
│   ├── conversations/     # Conversation management module
│   ├── messages/          # Messaging module
│   ├── templates/         # Template management module
│   ├── search/            # Search module
│   ├── channels/          # External channel adapters
│   ├── common/            # Shared utilities and guards
│   ├── app.module.ts      # Root application module
│   └── main.ts            # Application entry point
├── test/                  # Test files
└── package.json           # Dependencies and scripts
```

### Key Modules

#### Authentication Module
- JWT-based authentication
- User registration and login endpoints
- Password hashing with bcrypt
- Role-based access control

#### Users Module
- CRUD operations for user management
- Role assignment (Store Owner, CSKH)
- User status management (active/inactive)

#### Conversations Module
- Conversation lifecycle management
- Assignment functionality
- Status tracking (Open, Pending, Closed)

#### Messages Module
- Message persistence and retrieval
- Attachment handling
- Real-time message broadcasting

#### Templates Module
- Template creation, editing, and deletion
- Template categorization

#### Search Module
- Text search across conversations and messages
- Filtering and sorting capabilities

#### Channels Module
- Adapter pattern for external messaging platforms
- Facebook Messenger integration
- Zalo integration

## Frontend Implementation

### Technology Stack
- **Framework**: React.js (v18+)
- **State Management**: Redux Toolkit
- **UI Components**: Material-UI (MUI)
- **Routing**: React Router v6
- **Forms**: React Hook Form
- **Real-time**: Socket.IO client
- **HTTP Client**: Axios
- **Testing**: Jest, React Testing Library, Cypress

### Project Structure
```
frontend/
├── public/                # Static assets
├── src/
│   ├── components/        # Reusable UI components
│   ├── pages/             # Page components
│   ├── services/          # API service layer
│   ├── store/             # Redux store and slices
│   ├── hooks/             # Custom hooks
│   ├── utils/             # Utility functions
│   ├── App.js             # Root component
│   └── index.js           # Entry point
├── package.json           # Dependencies and scripts
└── ...
```

### Key Components

#### Authentication Components
- Login form
- Registration form
- Protected route wrappers

#### Conversation Components
- Conversation list view
- Conversation detail view
- Message history display
- Message input area

#### User Management Components
- User list table
- User creation/edit form
- Role assignment controls

#### Template Components
- Template list view
- Template creation/edit form

## Database Design

### MongoDB Collections

#### Users Collection
```javascript
{
  _id: ObjectId,
  username: String,
  email: String,
  password: String, // Hashed
  role: String, // 'store-owner' | 'cskh'
  status: String, // 'active' | 'inactive'
  createdAt: Date,
  updatedAt: Date
}
```

#### Conversations Collection
```javascript
{
  _id: ObjectId,
  channel: String, // 'facebook' | 'zalo'
  customerId: String, // External ID from channel
  customerName: String,
  assigneeId: ObjectId, // Reference to Users
  status: String, // 'open' | 'pending' | 'closed'
  lastMessageAt: Date,
  createdAt: Date,
  updatedAt: Date
}
```

#### Messages Collection
```javascript
{
  _id: ObjectId,
  conversationId: ObjectId, // Reference to Conversations
  senderId: ObjectId, // Reference to Users (null if customer)
  content: String,
  attachments: [String], // URLs to attached files
  timestamp: Date,
  isRead: Boolean
}
```

#### Templates Collection
```javascript
{
  _id: ObjectId,
  title: String,
  content: String,
  createdBy: ObjectId, // Reference to Users
  createdAt: Date,
  updatedAt: Date
}
```

### Indexes
- Compound index on Conversations: `{ channel: 1, customerId: 1 }`
- Index on Messages: `{ conversationId: 1, timestamp: -1 }`
- Index on Users: `{ email: 1 }`
- Text indexes for search functionality

## API Documentation

The API follows REST conventions and is documented using Swagger/OpenAPI. Key endpoints include:

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout

### Users
- `GET /api/users` - Get user list (Store Owner only)
- `GET /api/users/:id` - Get user details
- `PUT /api/users/:id` - Update user
- `DELETE /api/users/:id` - Delete user (Store Owner only)

### Conversations
- `GET /api/conversations` - Get conversation list
- `GET /api/conversations/:id` - Get conversation details
- `PUT /api/conversations/:id/assign` - Assign conversation
- `PUT /api/conversations/:id/status` - Update status

### Messages
- `POST /api/conversations/:id/messages` - Send message

### Templates
- `GET /api/templates` - Get template list
- `POST /api/templates` - Create template
- `PUT /api/templates/:id` - Update template
- `DELETE /api/templates/:id` - Delete template

### Search
- `GET /api/search/conversations` - Search conversations

## Real-time Communication

### Technology
- Socket.IO for real-time messaging
- WebSocket protocol with fallback to HTTP polling

### Events
- `message:new` - New message received
- `message:read` - Message read status updated
- `conversation:assigned` - Conversation assigned to user
- `conversation:status` - Conversation status updated
- `user:typing` - User typing indicator

### Implementation
- Server-side: Socket.IO integrated with NestJS Gateway
- Client-side: Socket.IO client in React application
- Authentication: JWT token passed during connection handshake

## Security Considerations

### Authentication & Authorization
- JWT tokens for stateless authentication
- Role-based access control (RBAC)
- Secure password storage with bcrypt
- Rate limiting to prevent brute force attacks

### Data Protection
- HTTPS encryption for all data in transit
- MongoDB encryption at rest (when using MongoDB Atlas)
- Input validation and sanitization
- Proper error handling without exposing system details

### API Security
- CORS configuration to restrict origins
- Helmet.js for securing HTTP headers
- Request validation using class-validator
- Dependency scanning for vulnerabilities

## Deployment

### Environment Setup
- Development, staging, and production environments
- Environment-specific configuration files
- Docker containers for consistent deployment

### CI/CD Pipeline
- Automated testing on each commit
- Automated deployment to staging
- Manual approval for production deployment

### Infrastructure
- Load balancer for high availability
- MongoDB cluster for data persistence
- Redis for caching (if needed)
- CDN for static assets

### Monitoring & Logging
- Application performance monitoring (APM)
- Centralized logging solution
- Health checks for all services
- Alerting for critical system events

## Testing

### Testing Strategy
- Unit testing for individual components and functions
- Integration testing for API endpoints
- End-to-end testing for key user flows
- Performance testing for scalability
- Security testing for vulnerabilities

### Tools
- Jest for unit and integration tests
- React Testing Library for frontend component tests
- Cypress for end-to-end tests
- Artillery or k6 for load testing
- OWASP ZAP for security testing

### Test Coverage Goals
- 80%+ code coverage for backend services
- 70%+ code coverage for frontend components
- 100% coverage for authentication and authorization logic
- Comprehensive testing for real-time communication features