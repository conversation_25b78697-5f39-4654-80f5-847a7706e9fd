# User Story: G<PERSON><PERSON> tệp đính kèm trong tin nhắn

**<PERSON><PERSON> một** nhân viên CSKH,
**Tôi muốn** gửi tệp đính kèm (hình ảnh, tài liệu) trong tin nhắn,
**Đ<PERSON>** cung cấp thông tin chi tiết hơn cho khách hàng.

## Tiêu chí chấp nhận
- [ ] Người dùng có thể chọn tệp đính kèm từ máy tính
- [ ] Hệ thống tải lên tệp và tạo liên kết truy cập
- [ ] Khách hàng có thể xem và tải xuống tệp đính kèm
- [ ] Tệp đính kèm được lưu trữ an toàn và liên kết với tin nhắn tương ứng

## Nhiệm vụ
### Task 1: Thiết kế giao diện gửi tệp đ<PERSON>h kèm
- [ ] Thêm nút chọn tệp vào giao diện nhập tin nhắn
- [ ] Triển khai preview tệp trước khi gửi
- [ ] Hiển thị tiến trình tải lên

### Task 2: Triển khai API tải lên tệp đính kèm
- [ ] Tạo endpoint POST /api/messages/:id/attachments để tải lên tệp
- [ ] Triển khai logic lưu trữ tệp và tạo liên kết truy cập
- [ ] Xác thực loại tệp và kích thước cho phép

### Task 3: Cập nhật API gửi tin nhắn với tệp đính kèm
- [ ] Cập nhật endpoint POST /api/conversations/:id/messages để hỗ trợ tệp đính kèm
- [ ] Triển khai logic gửi tệp đính kèm đến kênh bên ngoài
- [ ] Lưu thông tin tệp đính kèm vào database

### Task 4: Kết nối frontend với backend
- [ ] Cập nhật service gọi API để tải lên tệp đính kèm
- [ ] Triển khai hiển thị tệp đính kèm trong giao diện tin nhắn
- [ ] Xử lý lỗi tải lên và hiển thị thông báo phù hợp

## Ghi chú phát triển
- Cần tích hợp dịch vụ lưu trữ tệp (S3, Cloud Storage, hoặc local storage)
- Xử lý các loại tệp khác nhau (hình ảnh, PDF, tài liệu)
- Đảm bảo bảo mật và quyền truy cập tệp đính kèm

## Kiểm thử
- [ ] Kiểm thử tải lên tệp đính kèm
- [ ] Kiểm thử gửi tệp đính kèm trong tin nhắn
- [ ] Kiểm thử hiển thị tệp đính kèm trong giao diện
- [ ] Kiểm thử tải xuống tệp đính kèm bởi khách hàng

## QA Results
Pending

## Dev Agent Record
### Tasks / Subtasks Completion
- [ ] Task 1: Thiết kế giao diện gửi tệp đính kèm
- [ ] Task 2: Triển khai API tải lên tệp đính kèm
- [ ] Task 3: Cập nhật API gửi tin nhắn với tệp đính kèm
- [ ] Task 4: Kết nối frontend với backend

### Agent Model Used
Pending

### Debug Log References
Pending

### Completion Notes List
Pending

### File List
Pending

### Change Log
Pending

## Story Sequence
13