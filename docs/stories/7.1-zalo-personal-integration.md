# User Story: T<PERSON><PERSON> hợp <PERSON> cá nhân

**<PERSON><PERSON> một** hệ thống Omnichannel Inbox,
**Tôi muốn** nhận và gửi tin nhắn qua Zalo cá nhân,
**Để** hỗ trợ khách hàng trên nền tảng phổ biến tại Việt Nam.

## Tiêu chí chấp nhận

- [ ] Hệ thống có thể kết nối với tài khoản Zalo cá nhân thông qua thư viện zca-js
- [ ] Tin nhắn từ khách hàng trên Zalo cá nhân được nhận và lưu vào database
- [ ] Tin nhắn từ nhân viên được gửi đến khách hàng trên Zalo cá nhân
- [ ] Thông tin khách hàng từ Zalo cá nhân được đồng bộ chính xác
- [ ] H<PERSON> thống xử lý rate limiting từ Zalo để tránh bị block tài khoản
- [ ] Thông tin đăng nhập <PERSON>alo cá nhân được mã hóa khi lưu vào database

## Nhiệm vụ

### Task 1: Nghiên cứu và tích hợp thư viện zca-js

- [x] Nghiên cứu cách thức hoạt động của thư viện zca-js
- [ ] Tạo tài khoản Zalo test cho mục đích phát triển
- [x] Triển khai proof of concept kết nối với Zalo cá nhân

### Task 2: Thiết kế Zalo Personal Adapter

- [x] Tạo class ZaloPersonalAdapter implement ChannelAdapter interface
- [x] Triển khai logic nhận tin nhắn từ Zalo cá nhân
- [x] Triển khai logic gửi tin nhắn đến Zalo cá nhân

### Task 3: Đồng bộ thông tin khách hàng

- [ ] Triển khai logic lấy thông tin khách hàng từ Zalo cá nhân
- [ ] Lưu thông tin khách hàng vào database
- [ ] Cập nhật thông tin khách hàng khi có thay đổi

### Task 4: Tạo giao diện cấu hình Zalo cá nhân

- [ ] Thiết kế UI cho cấu hình Zalo cá nhân trong trang quản lý kênh
- [ ] Triển khai form nhập thông tin đăng nhập Zalo cá nhân
- [ ] Thêm cảnh báo về rủi ro bảo mật khi sử dụng Zalo cá nhân

## Ghi chú phát triển

- Thư viện zca-js là unofficial, có thể vi phạm điều khoản sử dụng của Zalo
- Cần xử lý rate limiting từ phía Zalo để tránh bị block tài khoản
- Cần mã hóa thông tin đăng nhập Zalo cá nhân khi lưu vào database
- Xử lý các loại tin nhắn khác nhau (text, image, attachment)
- Cần có cơ chế fallback nếu thư viện zca-js ngừng hoạt động

## Kiểm thử

- [ ] Kiểm thử kết nối với tài khoản Zalo cá nhân
- [ ] Kiểm thử nhận tin nhắn từ Zalo cá nhân
- [ ] Kiểm thử gửi tin nhắn đến Zalo cá nhân
- [ ] Kiểm thử đồng bộ thông tin khách hàng
- [ ] Kiểm thử khả năng xử lý rate limiting