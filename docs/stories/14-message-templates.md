# User Story: Sử dụng mẫu tin nhắn

**<PERSON><PERSON> một** nhân viên CSKH,
**Tôi muốn** sử dụng các mẫu tin nhắn được tạo sẵn,
**Để** phản hồi nhanh các câu hỏi thường gặp của khách hàng.

## Tiêu chí chấp nhận
- [ ] Người dùng có thể xem danh sách các mẫu tin nhắn đã tạo
- [ ] Người dùng có thể chọn và chèn mẫu tin nhắn vào ô soạn thảo
- [ ] Mẫu tin nhắn có thể chứa các biến động (tên kh<PERSON>ch hàng, mã đơn hàng, v.v.)
- [ ] Người dùng có thể tạo và chỉnh sửa mẫu tin nhắn mới

## Nhiệm vụ
### Task 1: Thiết kế giao diện quản lý mẫu tin nhắn
- [ ] Tạo trang danh sách mẫu tin nhắn
- [ ] Triển khai component tạo/chỉnh sửa mẫu tin nhắn
- [ ] Thêm dropdown chọn mẫu vào giao diện nhập tin nhắn

### Task 2: Triển khai API quản lý mẫu tin nhắn
- [ ] Tạo endpoint CRUD cho mẫu tin nhắn (/api/templates)
- [ ] Triển khai logic lưu trữ mẫu tin nhắn trong database
- [ ] Hỗ trợ biến động trong mẫu tin nhắn

### Task 3: Kết nối frontend với backend
- [ ] Cập nhật service gọi API để quản lý mẫu tin nhắn
- [ ] Triển khai state management cho mẫu tin nhắn
- [ ] Xử lý chèn mẫu tin nhắn vào ô soạn thảo

## Ghi chú phát triển
- Cần định nghĩa syntax cho biến động trong mẫu tin nhắn
- Xử lý preview mẫu tin nhắn với dữ liệu thực tế
- Đảm bảo UX mượt mà khi chọn và chèn mẫu tin nhắn

## Kiểm thử
- [ ] Kiểm thử tạo mẫu tin nhắn mới
- [ ] Kiểm thử chỉnh sửa mẫu tin nhắn
- [ ] Kiểm thử chèn mẫu tin nhắn vào ô soạn thảo
- [ ] Kiểm thử xử lý biến động trong mẫu tin nhắn

## QA Results
Pending

## Dev Agent Record
### Tasks / Subtasks Completion
- [ ] Task 1: Thiết kế giao diện quản lý mẫu tin nhắn
- [ ] Task 2: Triển khai API quản lý mẫu tin nhắn
- [ ] Task 3: Kết nối frontend với backend

### Agent Model Used
Pending

### Debug Log References
Pending

### Completion Notes List
Pending

### File List
Pending

### Change Log
Pending

## Story Sequence
14