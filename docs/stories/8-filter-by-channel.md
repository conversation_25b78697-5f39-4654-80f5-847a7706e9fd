# User Story: <PERSON><PERSON><PERSON> cuộ<PERSON> hội thoại theo kênh

**<PERSON><PERSON> một** nhân viên CSKH hoặc chủ shop,
**Tôi muốn** lọc danh sách cuộc hội thoại theo kênh (Facebook, Zalo),
**Để** tập trung vào các cuộc hội thoại từ một kênh cụ thể.

## Tiêu chí chấp nhận
- [ ] Người dùng có thể chọn kênh từ dropdown filter
- [ ] Danh sách cuộc hội thoại được cập nhật ngay lập tức khi chọn filter
- [ ] Người dùng có thể bỏ filter để xem tất cả các kênh
- [ ] Filter kênh hoạt động cùng với các filter khác

## Nhiệm vụ
### Task 1: Thiết kế giao diện filter kênh
- [ ] Thêm dropdown chọn kênh vào giao diện danh sách cuộc hội thoại
- [ ] Triển khai logic cập nhật UI khi chọn filter
- [ ] Thêm nút clear filter

### Task 2: Cập nhật API danh sách cuộc hội thoại
- [ ] Thêm tham số channel vào endpoint GET /api/conversations
- [ ] Triển khai logic filter theo kênh trong database query
- [ ] Đảm bảo filter kênh hoạt động với các filter khác

### Task 3: Kết nối frontend với backend
- [ ] Cập nhật service gọi API để truyền tham số channel
- [ ] Triển khai state management cho filter kênh
- [ ] Xử lý cập nhật danh sách real-time với filter

## Ghi chú phát triển
- Cần tối ưu query database để filter hiệu quả
- Xử lý trường hợp không có dữ liệu cho kênh đã chọn
- Đảm bảo UX mượt mà khi thay đổi filter

## Kiểm thử
- [ ] Kiểm thử filter theo từng kênh
- [ ] Kiểm thử kết hợp filter kênh với filter khác
- [ ] Kiểm thử clear filter

## QA Results
Pending

## Dev Agent Record
### Tasks / Subtasks Completion
- [ ] Task 1: Thiết kế giao diện filter kênh
- [ ] Task 2: Cập nhật API danh sách cuộc hội thoại
- [ ] Task 3: Kết nối frontend với backend

### Agent Model Used
Pending

### Debug Log References
Pending

### Completion Notes List
Pending

### File List
Pending

### Change Log
Pending

## Story Sequence
8