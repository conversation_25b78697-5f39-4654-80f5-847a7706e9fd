# User Story: <PERSON><PERSON><PERSON> hợp <PERSON>alo <PERSON>

**<PERSON><PERSON> một** hệ thống Omnichannel Inbox,
**Tôi muốn** nhận và gửi tin nhắn qua Zalo OA,
**Để** cung cấp dịch vụ chăm sóc khách hàng chuyên nghiệp hơn.

## Tiêu chí chấp nhận

- [ ] Hệ thống có thể kết nối với Zalo OA thông qua API chính thức
- [ ] Tin nhắn từ khách hàng trên Zalo OA được nhận và lưu vào database
- [ ] Tin nhắn từ nhân viên được gửi đến khách hàng trên Zalo OA
- [ ] Thông tin khách hàng từ Zalo OA được đồng bộ chính xác
- [ ] Hệ thống xử lý được OAuth2 authentication với Zalo OA
- [ ] Hệ thống xử lý webhook events từ Zalo OA API
- [ ] <PERSON><PERSON> thống có thể gửi tin nhắn theo các định dạng được hỗ trợ của Zalo OA

## Nhiệm vụ

### Task 1: Nghiên cứu Zalo OA API

- [ ] Nghiên cứu tài liệu API chính thức của Zalo OA
- [ ] Đăng ký tài khoản Zalo OA test cho mục đích phát triển
- [ ] Tìm hiểu về OAuth2 flow và webhook events

### Task 2: Thiết kế Zalo OA Adapter

- [ ] Tạo class ZaloOaAdapter implement ChannelAdapter interface
- [ ] Triển khai OAuth2 authentication flow
- [ ] Triển khai logic nhận tin nhắn từ Zalo OA
- [ ] Triển khai logic gửi tin nhắn đến Zalo OA

### Task 3: Xử lý webhook events

- [ ] Thiết lập endpoint để nhận webhook events từ Zalo OA
- [ ] Triển khai logic xử lý các loại event khác nhau từ Zalo OA
- [ ] Đảm bảo tính bảo mật cho webhook endpoint

### Task 4: Đồng bộ thông tin khách hàng

- [ ] Triển khai logic lấy thông tin khách hàng từ Zalo OA
- [ ] Lưu thông tin khách hàng vào database
- [ ] Cập nhật thông tin khách hàng khi có thay đổi

### Task 5: Tạo giao diện cấu hình Zalo OA

- [ ] Thiết kế UI cho cấu hình Zalo OA trong trang quản lý kênh
- [ ] Triển khai OAuth2 flow trong giao diện người dùng
- [ ] Hiển thị thông tin Zalo OA đã kết nối

## Ghi chú phát triển

- Zalo OA yêu cầu xác thực doanh nghiệp để sử dụng API
- Cần xử lý các loại tin nhắn khác nhau được hỗ trợ bởi Zalo OA API
- Cần có cơ chế retry khi gửi tin nhắn thất bại
- Cần theo dõi và xử lý rate limiting từ Zalo OA API
- Cần xử lý các webhook events như follow, unfollow, message, etc.

## Kiểm thử

- [ ] Kiểm thử OAuth2 authentication flow
- [ ] Kiểm thử kết nối với Zalo OA
- [ ] Kiểm thử nhận tin nhắn từ Zalo OA
- [ ] Kiểm thử gửi tin nhắn đến Zalo OA
- [ ] Kiểm thử đồng bộ thông tin khách hàng
- [ ] Kiểm thử xử lý webhook events
- [ ] Kiểm thử khả năng xử lý rate limiting