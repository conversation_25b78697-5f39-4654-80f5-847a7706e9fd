# User Story: Cậ<PERSON> nhật trạng thái cuộc hội thoại

**<PERSON><PERSON> một** nhân viên CSKH,
**Tôi muốn** cập nhật trạng thái của cuộc hội thoại (Mở, <PERSON><PERSON>ý, <PERSON><PERSON> đóng),
**Để** theo dõi tiến trình xử lý các yêu cầu khách hàng.

## Tiêu chí chấp nhận
- [ ] Người dùng có thể chọn trạng thái mới từ dropdown trong giao diện chi tiết cuộc hội thoại
- [ ] Hệ thống cập nhật trạng thái ngay lập tức và hiển thị cho người dùng khác
- [ ] Lịch sử thay đổi trạng thái được lưu trữ để truy xuất
- [ ] <PERSON><PERSON> đóng cuộc hội thoạ<PERSON>, <PERSON><PERSON> thống ghi nhận thời gian đóng

## Nhiệm vụ
### Task 1: Thiết kế giao diện cập nhật trạng thái
- [ ] Thêm dropdown chọn trạng thái vào giao diện chi tiết cuộc hội thoại
- [ ] Triển khai logic cập nhật UI khi thay đổi trạng thái
- [ ] Hiển thị lịch sử thay đổi trạng thái

### Task 2: Cập nhật API trạng thái cuộc hội thoại
- [ ] Tạo endpoint PUT /api/conversations/:id/status để cập nhật trạng thái
- [ ] Triển khai logic cập nhật trạng thái trong database
- [ ] Lưu lịch sử thay đổi trạng thái

### Task 3: Kết nối frontend với backend
- [ ] Cập nhật service gọi API để cập nhật trạng thái
- [ ] Triển khai state management cho trạng thái cuộc hội thoại
- [ ] Xử lý cập nhật real-time trạng thái cho người dùng khác

## Ghi chú phát triển
- Cần kiểm tra quyền trước khi cho phép cập nhật trạng thái
- Xử lý trường hợp cập nhật trạng thái không hợp lệ
- Đảm bảo UX mượt mà khi thay đổi trạng thái

## Kiểm thử
- [ ] Kiểm thử cập nhật trạng thái cuộc hội thoại
- [ ] Kiểm thử lưu lịch sử thay đổi trạng thái
- [ ] Kiểm thử cập nhật real-time trạng thái
- [ ] Kiểm thử xử lý lỗi khi cập nhật trạng thái không hợp lệ

## QA Results
Pending

## Dev Agent Record
### Tasks / Subtasks Completion
- [ ] Task 1: Thiết kế giao diện cập nhật trạng thái
- [ ] Task 2: Cập nhật API trạng thái cuộc hội thoại
- [ ] Task 3: Kết nối frontend với backend

### Agent Model Used
Pending

### Debug Log References
Pending

### Completion Notes List
Pending

### File List
Pending

### Change Log
Pending

## Story Sequence
12