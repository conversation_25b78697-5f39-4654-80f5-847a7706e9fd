# User Story: <PERSON><PERSON><PERSON> thực giao diện cấu hình <PERSON>alo cá nhân

**L<PERSON> một** người quản trị hệ thống,
**Tôi muốn** có giao diện người dùng để cấu hình và quản lý tài khoản Zalo cá nhân,
**Để** dễ dàng kết nối và theo dõi trạng thái của các tài khoản này trong hệ thống Omnichannel Inbox.

## Tiêu chí chấp nhận

- [ ] Giao diện người dùng cho phép thêm mới, chỉnh sửa, bật/tắt tài khoản Zalo cá nhân.
- [ ] Form cấu hình có các trường: Tên tài kho<PERSON> (tu<PERSON> chọn), Số điện thoại hoặc User ID, Mật khẩu hoặc Token.
- [ ] <PERSON><PERSON> cảnh bá<PERSON> bảo mật rõ ràng khi cấu hình tài khoản <PERSON>alo cá nhân.
- [ ] <PERSON><PERSON> nút "Test Connection" để kiểm tra thông tin đăng nhập trước khi lưu.
- [ ] Hiển thị trạng thái kết nối (connected/disconnected/error) của từng tài khoản.
- [ ] Giao diện phản hồi (responsive) và thân thiện trên các thiết bị.

## Nhiệm vụ

### Task 1: Tạo các component React cần thiết

- [ ] Tạo component `ZaloPersonalConfigCard` để hiển thị thông tin cơ bản và trạng thái của một tài khoản Zalo cá nhân.
- [ ] Tạo component `ZaloPersonalConfigModal` chứa form cấu hình và logic cho việc thêm/sửa.
- [ ] Tạo component `ZaloSecurityWarning` để hiển thị cảnh báo bảo mật có thể tái sử dụng.
- [ ] Tạo component `ZaloConnectionStatus` để hiển thị trạng thái kết nối một cách trực quan.
- [ ] Tạo service `ZaloPersonalConfigService` để gọi các API liên quan đến cấu hình Zalo cá nhân.

### Task 2: Cập nhật trang cấu hình kênh

- [ ] Tích hợp `ZaloPersonalConfigCard` vào trang `ChannelConfigurationPage`.
- [ ] Thêm logic để mở `ZaloPersonalConfigModal` khi người dùng nhấn "Add Channel" hoặc "Edit".
- [ ] Gọi API để tải danh sách cấu hình Zalo cá nhân khi trang được load.

### Task 3: Hiện thực logic trong modal

- [ ] Bind dữ liệu vào form trong `ZaloPersonalConfigModal`.
- [ ] Implement logic cho nút "Test Connection" (gọi API test, hiển thị kết quả).
- [ ] Implement logic cho nút "Save" (gọi API tạo/cập nhật cấu hình, mã hoá thông tin nhạy cảm trước khi gửi nếu cần ở frontend hoặc để backend xử lý).
- [ ] Đảm bảo form validation cơ bản (required fields).

### Task 4: Đảm bảo bảo mật và UX

- [ ] Không hiển thị mật khẩu/token dạng plain text trong form hoặc card sau khi lưu.
- [ ] Luôn hiển thị `ZaloSecurityWarning` một cách nổi bật trong modal.
- [ ] Hiển thị trạng thái kết nối chính xác dựa trên dữ liệu từ API.

### Task 5: Kiểm thử

- [ ] Viết unit test cho các component chính (`ZaloPersonalConfigCard`, `ZaloPersonalConfigModal`).
- [ ] Kiểm thử thủ công luồng thêm mới, chỉnh sửa, bật/tắt tài khoản.
- [ ] Kiểm thử chức năng "Test Connection".
- [ ] Kiểm tra giao diện trên các kích thước màn hình khác nhau (responsive).

## Ghi chú phát triển

- Tham khảo đặc tả thiết kế frontend tại `docs/frontend-spec/zalo-personal-frontend-design.md`.
- Các API endpoint cần thiết: `GET /api/channels/zalo-personal/config`, `POST /api/channels/zalo-personal/config`, `PUT /api/channels/zalo-personal/config/:id`, `DELETE /api/channels/zalo-personal/config/:id`, `POST /api/channels/zalo-personal/test`.
- Ưu tiên UX và bảo mật, đặc biệt là phần cảnh báo rủi ro.
- Backend cho các API này cần được đảm bảo đã sẵn sàng.

## Kiểm thử

(Phần này sẽ được cập nhật bởi người đảm nhận story khi thực hiện kiểm thử.)

## QA Results

(Phần này sẽ được cập nhật bởi QA Engineer sau khi review.)

## Dev Agent Record

### Tasks / Subtasks Completion

(Phần này sẽ được cập nhật bởi Developer khi thực hiện các task.)

### Agent Model Used

(Phần này sẽ được cập nhật bởi Developer.)

### Debug Log References

(Phần này sẽ được cập nhật bởi Developer.)

### Completion Notes List

(Phần này sẽ được cập nhật bởi Developer.)

### File List

(Phần này sẽ được cập nhật bởi Developer.)

### Change Log

(Phần này sẽ được cập nhật bởi Developer.)

## Story Sequence

8

## Status

Draft