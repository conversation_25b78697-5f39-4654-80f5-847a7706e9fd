# User Story: Tìm kiếm cuộc hội thoại

**<PERSON><PERSON> một** nhân viên CSKH hoặc chủ shop,
**Tôi muốn** tìm kiếm các cuộc hội thoại bằng từ khóa,
**Để** nhanh chóng tìm lại các cuộc trò chuyện trước đó.

## Tiêu chí chấp nhận
- [ ] Người dùng có thể nhập từ khóa vào ô tìm kiếm
- [ ] Hệ thống tìm kiếm theo tên khách hàng và nội dung tin nhắn
- [ ] Kết quả tìm kiếm được hiển thị ngay lập tức khi nhập
- [ ] Người dùng có thể xóa tìm kiếm để quay lại danh sách đầy đủ

## Nhiệm vụ
### Task 1: Thiết kế giao diện tìm kiếm
- [ ] Thêm ô tìm kiếm vào giao diện danh sách cuộc hội thoại
- [ ] Triển khai logic tìm kiếm real-time khi nhập từ khóa
- [ ] Thêm nút clear tìm kiếm

### Task 2: Cập nhật API tìm kiếm cuộc hội thoại
- [ ] Tạo endpoint GET /api/search/conversations để tìm kiếm cuộc hội thoại
- [ ] Triển khải logic tìm kiếm full-text trong database
- [ ] Tối ưu hiệu suất cho tìm kiếm

### Task 3: Kết nối frontend với backend
- [ ] Cập nhật service gọi API để tìm kiếm cuộc hội thoại
- [ ] Triển khai state management cho tìm kiếm
- [ ] Xử lý kết quả tìm kiếm và hiển thị trong UI

## Ghi chú phát triển
- Cần tối ưu query database để tìm kiếm hiệu quả
- Xử lý trường hợp không có kết quả tìm kiếm
- Đảm bảo UX mượt mà khi tìm kiếm real-time

## Kiểm thử
- [ ] Kiểm thử tìm kiếm theo tên khách hàng
- [ ] Kiểm thử tìm kiếm theo nội dung tin nhắn
- [ ] Kiểm thử tìm kiếm với từ khóa không dấu
- [ ] Kiểm thử clear tìm kiếm

## QA Results
Pending

## Dev Agent Record
### Tasks / Subtasks Completion
- [ ] Task 1: Thiết kế giao diện tìm kiếm
- [ ] Task 2: Cập nhật API tìm kiếm cuộc hội thoại
- [ ] Task 3: Kết nối frontend với backend

### Agent Model Used
Pending

### Debug Log References
Pending

### Completion Notes List
Pending

### File List
Pending

### Change Log
Pending

## Story Sequence
11