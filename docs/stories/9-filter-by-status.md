# User Story: <PERSON><PERSON><PERSON> cu<PERSON> hội thoại theo trạng thái

**<PERSON><PERSON> một** nhân viên CSKH hoặc chủ shop,
**Tôi muốn** lọc danh sách cuộc hội thoại theo trạng thái (Mở, <PERSON><PERSON>ý, <PERSON><PERSON> đóng),
**Để** tập trung vào các cuộc hội thoại cần được xử lý.

## Tiêu chí chấp nhận
- [ ] Người dùng có thể chọn trạng thái từ dropdown filter
- [ ] Danh sách cuộc hội thoại được cập nhật ngay lập tức khi chọn filter
- [ ] Người dùng có thể chọn nhiều trạng thái cùng lúc
- [ ] Filter trạng thái hoạt động cùng với các filter khác

## Nhiệm vụ
### Task 1: Thiết kế giao diện filter trạng thái
- [ ] Thêm dropdown chọn trạng thái vào giao diện danh sách cuộc hội thoại
- [ ] Triển khai logic chọn nhiều trạng thái cùng lúc
- [ ] Thêm nút clear filter trạng thái

### Task 2: Cập nhật API danh sách cuộc hội thoại
- [ ] Thêm tham số status vào endpoint GET /api/conversations
- [ ] Triển khai logic filter theo trạng thái trong database query
- [ ] Hỗ trợ filter nhiều trạng thái cùng lúc

### Task 3: Kết nối frontend với backend
- [ ] Cập nhật service gọi API để truyền tham số status
- [ ] Triển khai state management cho filter trạng thái
- [ ] Xử lý cập nhật danh sách real-time với filter

## Ghi chú phát triển
- Cần tối ưu query database để filter hiệu quả
- Xử lý trường hợp không có dữ liệu cho trạng thái đã chọn
- Đảm bảo UX mượt mà khi thay đổi filter

## Kiểm thử
- [ ] Kiểm thử filter theo từng trạng thái
- [ ] Kiểm thử filter theo nhiều trạng thái cùng lúc
- [ ] Kiểm thử kết hợp filter trạng thái với filter khác
- [ ] Kiểm thử clear filter trạng thái

## QA Results
Pending

## Dev Agent Record
### Tasks / Subtasks Completion
- [ ] Task 1: Thiết kế giao diện filter trạng thái
- [ ] Task 2: Cập nhật API danh sách cuộc hội thoại
- [ ] Task 3: Kết nối frontend với backend

### Agent Model Used
Pending

### Debug Log References
Pending

### Completion Notes List
Pending

### File List
Pending

### Change Log
Pending

## Story Sequence
9