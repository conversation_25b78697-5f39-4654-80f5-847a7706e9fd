# User Story: <PERSON><PERSON> cuộc hội thoại cho nhân viên

**<PERSON><PERSON> một** chủ shop hoặc nhân viên CSKH,
**Tôi muốn** gán cuộc hội thoại cho một nhân viên cụ thể,
**Để** phân công công việc hiệu quả trong nhóm.

## Tiêu chí chấp nhận
- [ ] Người dùng có thể chọn cuộc hội thoại và chọn nhân viên để gán
- [ ] Hệ thống cập nhật người được gán ngay lập tức
- [ ] Người được gán nhận được thông báo về cuộc hội thoại mới
- [ ] Chủ shop có thể gán bất kỳ cuộc hội thoại nào
- [ ] Nhân viên CSKH có thể tự gán cuộc hội thoại cho chính họ

## Nhiệ<PERSON> vụ
### Task 1: Thiết kế giao diện gán cuộc hội thoại
- [ ] Thêm dropdown chọn nhân viên vào giao diện danh sách và chi tiết cuộc hội thoại
- [ ] Triển khai logic cập nhật UI khi gán cuộc hội thoại
- [ ] Thêm nút gán cho chính mình

### Task 2: Cập nhật API gán cuộc hội thoại
- [ ] Tạo endpoint PUT /api/conversations/:id/assign để gán cuộc hội thoại
- [ ] Triển khai logic cập nhật assignee trong database
- [ ] Gửi thông báo real-time đến nhân viên được gán

### Task 3: Kết nối frontend với backend
- [ ] Cập nhật service gọi API để gán cuộc hội thoại
- [ ] Triển khai state management cho assignee
- [ ] Xử lý thông báo real-time khi được gán cuộc hội thoại

## Ghi chú phát triển
- Cần kiểm tra quyền trước khi cho phép gán cuộc hội thoại
- Xử lý trường hợp nhân viên không tồn tại hoặc không hoạt động
- Đảm bảo UX mượt mà khi gán cuộc hội thoại

## Kiểm thử
- [ ] Kiểm thử gán cuộc hội thoại bởi chủ shop
- [ ] Kiểm thử tự gán cuộc hội thoại bởi nhân viên
- [ ] Kiểm thử thông báo real-time khi được gán
- [ ] Kiểm thử xử lý lỗi khi gán cho nhân viên không hợp lệ

## QA Results
Pending

## Dev Agent Record
### Tasks / Subtasks Completion
- [ ] Task 1: Thiết kế giao diện gán cuộc hội thoại
- [ ] Task 2: Cập nhật API gán cuộc hội thoại
- [ ] Task 3: Kết nối frontend với backend

### Agent Model Used
Pending

### Debug Log References
Pending

### Completion Notes List
Pending

### File List
Pending

### Change Log
Pending

## Story Sequence
10