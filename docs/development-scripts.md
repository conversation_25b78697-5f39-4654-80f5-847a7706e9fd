# Development Scripts

## Starting the Backend
To start the backend in development mode with hot reload:

```bash
cd backend
npm run start:dev
```

The backend will be available at http://localhost:3000

## Starting the Frontend
To start the frontend in development mode with hot reload:

```bash
cd frontend
npm run dev
```

The frontend will be available at http://localhost:5173

## Starting Both Applications
To start both applications simultaneously, you can use a terminal multiplexer like `tmux` or `screen`, or run them in separate terminal windows.

Alternatively, you can use a tool like `concurrently` to run both applications in a single terminal:

1. Install concurrently globally:
   ```bash
   npm install -g concurrently
   ```

2. Run both applications:
   ```bash
   npm run dev
   ```

   This will start both the backend (on port 3000) and frontend (on port 5173) simultaneously.