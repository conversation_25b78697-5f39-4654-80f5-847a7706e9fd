# Frontend Specifications: Omnichannel Inbox System

## 1. Overview

### 1.1 Purpose
This document outlines the frontend specifications for the Omnichannel Inbox System. It details the user interface design, component structure, and interaction patterns for a web-based application that consolidates customer conversations from multiple channels.

### 1.2 Scope
The frontend application will be built using React.js and will include:
- User authentication screens
- Dashboard with conversation list
- Conversation detail view with messaging interface
- User management screens
- Template management screens
- Search functionality

### 1.3 Goals
- Create an intuitive and efficient interface for managing customer conversations
- Provide a consistent user experience across all features
- Ensure responsive design for various screen sizes
- Implement real-time updates for incoming messages
- Support role-based access control (Store Owner, CSKH)

## 2. User Interface Design

### 2.1 Design Principles
- **Simplicity**: Clean, uncluttered interface focused on core functionality
- **Efficiency**: Minimize clicks and actions needed to complete tasks
- **Consistency**: Uniform design patterns throughout the application
- **Accessibility**: WCAG 2.1 AA compliance
- **Responsiveness**: Adaptable layout for desktop, tablet, and mobile

### 2.2 Color Scheme
- **Primary Color**: #1976D2 (Blue) - For primary actions and key elements
- **Secondary Color**: #424242 (<PERSON> Gray) - For secondary actions and text
- **Accent Color**: #4CAF50 (Green) - For success states and positive actions
- **Warning Color**: #FFC107 (Amber) - For warnings and alerts
- **Error Color**: #F44336 (Red) - For errors and destructive actions
- **Background**: #F5F5F5 (Light Gray) - For main content areas
- **Surface**: #FFFFFF (White) - For cards and elevated elements

### 2.3 Typography
- **Font Family**: 'Roboto', 'Helvetica Neue', Arial, sans-serif
- **Font Sizes**:
  - Headings: 24px, 20px, 16px
  - Body Text: 14px
  - Captions: 12px

### 2.4 Layout Structure
The application will follow a standard layout with:
- **Top Navigation Bar**: Application logo, user profile, notifications
- **Main Sidebar**: Navigation menu with primary sections
- **Content Area**: Main content display
- **Right Sidebar**: Contextual information and actions (optional)

## 3. User Interface Components

### 3.1 Authentication Screens

#### 3.1.1 Login Screen
- Application logo and name
- Email input field
- Password input field
- "Remember me" checkbox
- "Login" button
- "Forgot password?" link
- "Register" link for new users

#### 3.1.2 Registration Screen
- Application logo and name
- Full name input field
- Email input field
- Password input field
- Password confirmation field
- Role selection (Store Owner, CSKH)
- "Register" button
- "Back to login" link

### 3.2 Dashboard

#### 3.2.1 Top Navigation Bar
- Application logo and name
- Search bar
- User profile dropdown (with logout option)
- Notifications bell icon

#### 3.2.2 Main Sidebar
- Navigation menu items:
  - Dashboard
  - Conversations
  - Templates
  - Users (Store Owner only)
  - Settings
- Channel integration status indicators

#### 3.2.3 Conversation List View
- Filter and sort controls:
  - Channel filter (Facebook, Zalo)
  - Status filter (Open, Pending, Closed)
  - Assignee filter
  - Date range filter
- Search input field
- Conversation list table with columns:
  - Customer name
  - Channel icon
  - Last message preview
  - Assignee
  - Status indicator
  - Last updated timestamp
- Pagination controls
- "New Conversation" button (where applicable)

#### 3.2.4 Conversation Item
- Customer avatar or initials
- Customer name
- Channel icon
- Last message preview (truncated)
- Assignee avatar
- Status badge (Open, Pending, Closed)
- Timestamp of last activity

### 3.3 Conversation Detail View

#### 3.3.1 Conversation Header
- Customer name and avatar
- Channel indicator
- Status dropdown
- Assignee selector
- "Close Conversation" button

#### 3.3.2 Message History
- Scrollable message list with:
  - Different styling for sent vs received messages
  - Timestamps for each message
  - Attachment previews (images, files)
  - Message status indicators (sent, delivered, read)
- "Load more" button for older messages

#### 3.3.3 Message Input Area
- Text input field with multi-line support
- Attachment button (for images/files)
- Template selector dropdown
- Emoji picker button
- "Send" button
- Real-time typing indicators

### 3.4 User Management

#### 3.4.1 User List View (Store Owner only)
- Filter controls:
  - Role filter (Store Owner, CSKH)
  - Status filter (Active, Inactive)
- Search input field
- User list table with columns:
  - User name
  - Email
  - Role
  - Status
  - Last login timestamp
- "Add User" button
- Pagination controls

#### 3.4.2 User Detail/Edit Form
- User avatar upload
- Full name input field
- Email input field
- Role selector (Store Owner, CSKH)
- Status toggle (Active/Inactive)
- "Save" and "Cancel" buttons
- "Delete User" button (with confirmation)

### 3.5 Template Management

#### 3.5.1 Template List View
- Search input field
- Template list with cards showing:
  - Template title
  - Preview of template content
  - Created by information
  - Last updated timestamp
- "New Template" button
- Pagination controls

#### 3.5.2 Template Creation/Edit Form
- Title input field
- Content text area with rich text editor
- "Save" and "Cancel" buttons
- "Delete Template" button (with confirmation for edit view)

### 3.6 Search Results View
- Search input field (persistent)
- Filter controls (similar to conversation list)
- Search results list with:
  - Conversation items matching search criteria
  - Highlighted search terms in message previews
- Pagination controls

## 4. User Interaction Flows

### 4.1 Authentication Flow
1. User navigates to login page
2. User enters credentials and submits
3. System validates credentials
4. On success: Redirect to dashboard
5. On failure: Show error message

### 4.2 Conversation Management Flow
1. User views conversation list on dashboard
2. User filters/sorts conversations as needed
3. User clicks on a conversation to view details
4. User reads message history
5. User composes and sends a reply
6. User assigns conversation to another agent (if needed)
7. User updates conversation status (if needed)

### 4.3 Message Sending Flow
1. User types message in input field
2. User can attach files/images
3. User can select from quick reply templates
4. User clicks "Send" button
5. Message is sent to backend and external channel
6. Message appears in conversation history
7. Real-time updates are pushed to other agents

### 4.4 User Management Flow (Store Owner)
1. Store Owner navigates to Users section
2. Store Owner views list of users
3. Store Owner can filter/search users
4. Store Owner clicks "Add User" to create new user
5. Store Owner fills user details and saves
6. Store Owner can edit existing user details
7. Store Owner can deactivate/delete users

### 4.5 Template Management Flow
1. User navigates to Templates section
2. User views list of existing templates
3. User clicks "New Template" to create
4. User fills template details and saves
5. User can edit existing templates
6. User can delete templates

## 5. Responsive Design

### 5.1 Desktop Layout (> 1024px)
- Full three-column layout (sidebar, content, right sidebar)
- All features accessible without scrolling

### 5.2 Tablet Layout (768px - 1024px)
- Collapsed sidebar that can be toggled
- Two-column layout (content, right sidebar)
- Stacked filter controls

### 5.3 Mobile Layout (< 768px)
- Hamburger menu for navigation
- Single column layout
- Simplified filter controls
- Modal dialogs for complex forms

## 6. Accessibility

### 6.1 WCAG Compliance
- Proper color contrast ratios
- Keyboard navigation support
- Screen reader compatibility
- Alternative text for images

### 6.2 ARIA Labels
- Descriptive labels for interactive elements
- Status announcements for dynamic content
- Form field descriptions

## 7. Performance Considerations

### 7.1 Loading States
- Skeleton screens for content loading
- Progress indicators for long operations
- Optimistic updates for user actions

### 7.2 Bundle Optimization
- Code splitting by route
- Lazy loading of non-critical components
- Image optimization and lazy loading

## 8. Technology Stack

### 8.1 Framework
- React.js (v18+) with hooks

### 8.2 State Management
- Redux Toolkit or Context API for global state
- React Query for server state management

### 8.3 UI Components
- Material-UI (MUI) for pre-built components
- Custom components for specific needs

### 8.4 Styling
- CSS Modules or Styled Components
- Responsive design with CSS Grid and Flexbox

### 8.5 Real-time Communication
- Socket.IO client for real-time messaging

### 8.6 Routing
- React Router v6 for navigation

### 8.7 Form Handling
- React Hook Form for form validation and management

### 8.8 Testing
- Jest for unit testing
- React Testing Library for component testing
- Cypress for end-to-end testing

## 9. Security Considerations

### 9.1 Frontend Security
- Input validation and sanitization
- Protection against XSS attacks
- Secure storage of authentication tokens

### 9.2 Data Protection
- Masking of sensitive information in UI
- Proper error handling without exposing system details

## 10. Internationalization

### 10.1 Language Support
- Primary language: Vietnamese
- Support for future language additions

### 10.2 Localization Strategy
- Separate translation files
- Date/time formatting based on locale
- RTL support for future languages

## 11. Browser Support

### 11.1 Supported Browsers
- Chrome (latest 2 versions)
- Firefox (latest 2 versions)
- Safari (latest 2 versions)
- Edge (latest 2 versions)

### 11.2 Polyfills
- Required polyfills for older browser support (if needed)

## 12. Conclusion

This frontend specification provides a comprehensive guide for implementing the user interface of the Omnichannel Inbox System. The design focuses on usability, efficiency, and a consistent experience across all features. The component breakdown and interaction flows will guide the development process, ensuring a high-quality user experience that meets the needs of both Store Owners and Customer Service Representatives.