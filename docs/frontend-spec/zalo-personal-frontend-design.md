# Zalo Personal Adapter - Frontend Design Document

## 1. Overview

This document outlines the frontend design for configuring and managing Zalo Personal accounts in the Omnichannel Inbox System.

## 2. Requirements

### 2.1 Functional Requirements
- Create UI for configuring Zalo Personal accounts
- Implement form for entering Zalo Personal account credentials
- Display security warnings about using personal accounts
- Show status of Zalo Personal account connections
- Allow enabling/disabling Zalo Personal accounts

### 2.2 Non-Functional Requirements
- Responsive design for various screen sizes
- Clear security warnings about personal account usage
- Intuitive user interface for configuration
- Consistent with existing UI components

## 3. UI Components

### 3.1 Channel Configuration Page
The channel configuration page will be updated to include Zalo Personal accounts alongside other channel types.

#### 3.1.1 Zalo Personal Configuration Card
- Channel type icon (Zalo icon)
- Account identifier (if configured)
- Connection status indicator (connected/disconnected)
- Configure/Edit button
- Enable/Disable toggle

### 3.2 Zalo Personal Configuration Modal
A modal dialog for configuring Zalo Personal accounts.

#### 3.2.1 Form Fields
- Account Name (optional, for identification)
- Login Method: Radio buttons to choose between "Manual (Phone/Password)" and "QR Code".
- Zalo Phone Number or User ID (Visible if Manual login is selected)
- Password or Authentication Token (Visible if Manual login is selected)
- QR Code Display Area (Visible if QR Code login is selected, an `<img>` or component to display the `base64` image)
- Rate Limiting Configuration
  - Max Requests per Time Window
  - Time Window (in seconds)

#### 3.2.2 Security Warning
A prominent warning section explaining the risks of using personal Zalo accounts:
- "Using personal Zalo accounts may violate Zalo's terms of service"
- "Personal accounts are more likely to be blocked due to rate limiting"
- "This method uses an unofficial library that may stop working at any time"
- "Credentials are encrypted but using personal accounts still carries risks"

#### 3.2.3 Actions
- Save (encrypted credentials to database)
- Cancel
- Test Connection (validate credentials without saving)

## 4. User Flows

### 4.1 Adding a New Zalo Personal Account (Manual)
1. User navigates to Channel Configuration page
2. User clicks "Add Channel" button
3. User selects "Zalo Personal" from channel type options
4. Zalo Personal Configuration modal opens
5. User selects "Manual (Phone/Password)" login method
6. User fills in account information (Phone/User ID, Password/Token)
7. User reads security warnings
8. User clicks "Test Connection" to validate credentials
9. User clicks "Save" to store encrypted configuration

### 4.2 Editing Existing Zalo Personal Account (Manual)
1. User navigates to Channel Configuration page
2. User finds Zalo Personal account card
3. User clicks "Edit" button
4. Zalo Personal Configuration modal opens with existing information
5. User ensures "Manual (Phone/Password)" login method is selected
6. User modifies account information
7. User clicks "Test Connection" to validate new credentials
8. User clicks "Save" to update configuration

### 4.3 Enabling/Disabling Zalo Personal Account
1. User navigates to Channel Configuration page
2. User finds Zalo Personal account card
3. User toggles the Enable/Disable switch
4. System updates account status in database

### 4.4 Adding a New Zalo Personal Account via QR Code
1. User navigates to Channel Configuration page.
2. User clicks "Add Channel" button.
3. User selects "Zalo Personal" from channel type options.
4. Zalo Personal Configuration modal opens.
5. User selects "Login via QR Code" option.
6. System calls `POST /api/channels/zalo-personal/genLoginQR`.
7. System receives a `base64` string representing the QR code image.
8. System displays the QR code image in the modal.
9. User opens Zalo app on their phone and scans the QR code.
10. System polls `GET /api/channels/zalo-personal/checkLoginStatus/:requestId` (or similar) periodically (e.g., every 2-3 seconds) to check login status.
11. Once login is successful, the system receives authentication tokens/cookies.
12. System automatically fills the internal configuration fields (e.g., `zaloId`, `accessToken`, `refreshToken` - these might be different from phone/password and should be handled securely).
13. User clicks "Save" to store the encrypted configuration (tokens/cookies) in the database.

## 5. API Integration

### 5.1 Endpoints
- `GET /api/channels/zalo-personal/config`: Fetch Zalo Personal configurations
- `POST /api/channels/zalo-personal/config`: Create new Zalo Personal configuration
- `PUT /api/channels/zalo-personal/config/:id`: Update existing Zalo Personal configuration
- `DELETE /api/channels/zalo-personal/config/:id`: Delete Zalo Personal configuration
- `POST /api/channels/zalo-personal/test`: Test Zalo Personal account connection
- `POST /api/channels/zalo-personal/genLoginQR`: Initiates the QR code login flow. Returns a `requestId` and a `base64` encoded QR code image.
- `GET /api/channels/zalo-personal/checkLoginStatus/:requestId`: Checks the status of the QR code login initiated by `genLoginQR`. Returns `pending`, `success` (with tokens), or `expired`.

### 5.2 Data Models

#### 5.2.1 ZaloPersonalConfig
```typescript
interface ZaloPersonalConfig {
  id: string;
  accountName?: string;
  phoneNumber: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  status: 'connected' | 'disconnected' | 'error';
}
```

## 6. Security Considerations

### 6.1 Credential Handling
- Never display decrypted credentials in the UI
- Show masked credentials in list views (e.g., ****1234)
- Clear form fields after saving or canceling

### 6.2 Security Warnings
- Display clear, prominent warnings about risks
- Require user acknowledgment for high-risk actions
- Provide links to documentation about risks

## 7. Responsive Design

### 7.1 Desktop Layout
- Full-width configuration cards
- Modal dialogs for configuration forms
- Multi-column layout for form fields

### 7.2 Mobile Layout
- Stacked configuration cards
- Full-screen modal dialogs
- Single-column layout for form fields
- Touch-friendly controls

## 8. Accessibility

### 8.1 WCAG Compliance
- Proper color contrast for all elements
- Keyboard navigation support
- Screen reader compatibility
- Focus indicators for interactive elements

### 8.2 Form Accessibility
- Proper labels for all form fields
- Error messages associated with form fields
- Instructions for complex interactions

## 9. Implementation Plan

### 9.1 Components to Create
1. ZaloPersonalConfigCard - Displays summary information for a Zalo Personal account
2. ZaloPersonalConfigModal - Modal dialog for configuring accounts
3. ZaloSecurityWarning - Reusable component for displaying security warnings
4. ZaloConnectionStatus - Component for displaying connection status
5. ZaloQRCodeDisplay - A reusable component to render a QR code image from a `base64` string.

### 9.2 Pages to Update
1. ChannelConfigurationPage - Add Zalo Personal account management

### 9.3 Services to Create
1. ZaloPersonalConfigService - Handle API calls for Zalo Personal configuration

## 10. Testing Strategy

### 10.1 Unit Tests
- Test ZaloPersonalConfigCard with different data states
- Test ZaloPersonalConfigModal form validation
- Test ZaloSecurityWarning display logic
- Test ZaloConnectionStatus with different status values
- Test ZaloQRCodeDisplay component with valid and invalid `base64` strings

### 10.2 Integration Tests
- Test complete flow of adding a new Zalo Personal account (Manual)
- Test editing existing Zalo Personal account (Manual)
- Test enabling/disabling Zalo Personal account
- Test error handling in UI
- Test the complete flow of adding a new Zalo Personal account via QR Code, including generating the QR, polling for status, and successful login.

### 10.3 End-to-End Tests
- Test responsive layout on different screen sizes
- Test accessibility features
- Test security warning display and interaction